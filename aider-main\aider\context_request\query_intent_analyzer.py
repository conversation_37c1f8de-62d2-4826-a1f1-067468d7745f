"""
Query Intent Analyzer
Analyzes user queries to understand their semantic intent and extract domain concepts.
"""

import re
from typing import List, Dict, Optional, Tuple
from intelligent_context_models import (
    QueryIntent, QueryScope, DomainConcept, QueryContext,
    QUERY_INTENT_PATTERNS, DOMAIN_CONCEPT_PATTERNS
)


class QueryIntentAnalyzer:
    """Analyze user queries to understand their semantic intent."""
    
    def __init__(self):
        self.intent_patterns = QUERY_INTENT_PATTERNS
        self.domain_patterns = DOMAIN_CONCEPT_PATTERNS
        
        # Common programming terms that indicate technical focus
        self.technical_indicators = {
            'function', 'method', 'class', 'module', 'component', 'service',
            'api', 'endpoint', 'handler', 'controller', 'processor', 'manager',
            'factory', 'builder', 'adapter', 'decorator', 'strategy', 'observer',
            'database', 'cache', 'storage', 'repository', 'dao', 'orm',
            'parser', 'validator', 'transformer', 'converter', 'formatter',
            'error', 'exception', 'logging', 'monitoring', 'metrics',
            'authentication', 'authorization', 'security', 'encryption',
            'configuration', 'settings', 'parameters', 'options'
        }
        
        # Business domain indicators
        self.business_indicators = {
            'user', 'customer', 'client', 'account', 'profile', 'session',
            'order', 'payment', 'transaction', 'invoice', 'billing',
            'product', 'service', 'catalog', 'inventory', 'stock',
            'workflow', 'process', 'business', 'rule', 'policy',
            'notification', 'alert', 'message', 'email', 'sms'
        }
    
    def analyze_query_intent(self, query: str) -> QueryIntent:
        """
        Classify query into semantic categories.
        
        Args:
            query: User query string
            
        Returns:
            QueryIntent classification
        """
        query_lower = query.lower()
        intent_scores = {}
        
        # Score each intent based on pattern matching
        for intent, patterns in self.intent_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    score += 1
            intent_scores[intent] = score
        
        # Additional heuristics for better classification

        # Debugging indicators
        if any(word in query_lower for word in ['error', 'bug', 'fail', 'issue', 'problem', 'broken', 'not working']):
            intent_scores[QueryIntent.DEBUGGING_ASSISTANCE] = intent_scores.get(QueryIntent.DEBUGGING_ASSISTANCE, 0) + 2

        # Implementation indicators
        if any(word in query_lower for word in ['add', 'create', 'implement', 'build', 'develop', 'extend']):
            intent_scores[QueryIntent.FEATURE_IMPLEMENTATION] = intent_scores.get(QueryIntent.FEATURE_IMPLEMENTATION, 0) + 2

        # Architecture indicators
        if any(word in query_lower for word in ['architecture', 'design', 'structure', 'overview', 'organization']):
            intent_scores[QueryIntent.ARCHITECTURE_UNDERSTANDING] = intent_scores.get(QueryIntent.ARCHITECTURE_UNDERSTANDING, 0) + 2

        # Workflow indicators
        if any(word in query_lower for word in ['flow', 'process', 'sequence', 'steps', 'workflow', 'pipeline']):
            intent_scores[QueryIntent.WORKFLOW_ANALYSIS] = intent_scores.get(QueryIntent.WORKFLOW_ANALYSIS, 0) + 2

        # Performance indicators
        if any(word in query_lower for word in ['performance', 'slow', 'fast', 'optimize', 'speed', 'efficiency']):
            intent_scores[QueryIntent.PERFORMANCE_ANALYSIS] = intent_scores.get(QueryIntent.PERFORMANCE_ANALYSIS, 0) + 2

        # Security indicators
        if any(word in query_lower for word in ['security', 'auth', 'permission', 'access', 'secure', 'vulnerability']):
            intent_scores[QueryIntent.SECURITY_ANALYSIS] = intent_scores.get(QueryIntent.SECURITY_ANALYSIS, 0) + 2
        
        # Return the intent with the highest score, default to COMPONENT_DISCOVERY
        if not intent_scores or max(intent_scores.values()) == 0:
            return QueryIntent.COMPONENT_DISCOVERY
            
        return max(intent_scores, key=intent_scores.get)
    
    def extract_domain_concepts(self, query: str) -> List[DomainConcept]:
        """
        Extract domain-specific concepts from query.
        
        Args:
            query: User query string
            
        Returns:
            List of domain concepts with confidence scores
        """
        concepts = []
        query_lower = query.lower()
        words = re.findall(r'\b\w+\b', query_lower)
        
        # Extract technical concepts
        technical_concepts = []
        for word in words:
            if word in self.technical_indicators:
                technical_concepts.append(word)
        
        if technical_concepts:
            concepts.append(DomainConcept(
                concept=', '.join(technical_concepts),
                category='technical',
                confidence=0.9,
                related_terms=technical_concepts,
                context_clues=self._extract_context_clues(query, technical_concepts)
            ))
        
        # Extract business concepts
        business_concepts = []
        for word in words:
            if word in self.business_indicators:
                business_concepts.append(word)
        
        if business_concepts:
            concepts.append(DomainConcept(
                concept=', '.join(business_concepts),
                category='business',
                confidence=0.8,
                related_terms=business_concepts,
                context_clues=self._extract_context_clues(query, business_concepts)
            ))
        
        # Extract data concepts using patterns
        for category, patterns in self.domain_patterns.items():
            category_concepts = []
            for pattern in patterns:
                matches = re.findall(pattern, query_lower)
                category_concepts.extend(matches)
            
            if category_concepts:
                # Remove duplicates and filter out concepts already found
                unique_concepts = list(set(category_concepts))
                existing_concepts = set()
                for concept in concepts:
                    existing_concepts.update(concept.related_terms)
                
                new_concepts = [c for c in unique_concepts if c not in existing_concepts]
                
                if new_concepts:
                    concepts.append(DomainConcept(
                        concept=', '.join(new_concepts),
                        category=category,
                        confidence=0.7,
                        related_terms=new_concepts,
                        context_clues=self._extract_context_clues(query, new_concepts)
                    ))
        
        return concepts
    
    def identify_query_scope(self, query: str, domain_concepts: List[DomainConcept]) -> QueryScope:
        """
        Determine the scope of analysis needed.
        
        Args:
            query: User query string
            domain_concepts: Extracted domain concepts
            
        Returns:
            QueryScope classification
        """
        query_lower = query.lower()
        
        # Single component indicators
        if any(word in query_lower for word in ['function', 'method', 'class', 'specific']):
            if any(word in query_lower for word in ['this', 'that', 'the']):
                return QueryScope.SINGLE_COMPONENT
        
        # System overview indicators
        if any(word in query_lower for word in ['overview', 'architecture', 'entire', 'whole', 'all', 'system']):
            return QueryScope.SYSTEM_OVERVIEW
        
        # Workflow chain indicators
        if any(word in query_lower for word in ['flow', 'process', 'sequence', 'chain', 'pipeline', 'workflow']):
            return QueryScope.WORKFLOW_CHAIN
        
        # Cross-cutting indicators
        if any(word in query_lower for word in ['across', 'throughout', 'everywhere', 'global', 'common']):
            return QueryScope.CROSS_CUTTING
        
        # Module level indicators
        if any(word in query_lower for word in ['module', 'package', 'namespace', 'directory']):
            return QueryScope.MODULE_LEVEL
        
        # Default based on number of concepts
        if len(domain_concepts) > 2:
            return QueryScope.WORKFLOW_CHAIN
        elif len(domain_concepts) == 1:
            return QueryScope.SINGLE_COMPONENT
        else:
            return QueryScope.MODULE_LEVEL
    
    def analyze_complete_query(self, query: str, focus_entities: Optional[List[str]] = None) -> QueryContext:
        """
        Perform complete query analysis.
        
        Args:
            query: User query string
            focus_entities: Optional list of entities to focus on
            
        Returns:
            Complete QueryContext with all analysis results
        """
        intent = self.analyze_query_intent(query)
        domain_concepts = self.extract_domain_concepts(query)
        scope = self.identify_query_scope(query, domain_concepts)
        
        # Calculate overall confidence based on pattern matches and concept extraction
        confidence = self._calculate_confidence(query, intent, domain_concepts)
        
        # Extract query patterns for debugging
        query_patterns = self._extract_query_patterns(query)
        
        return QueryContext(
            intent=intent,
            domain_concepts=domain_concepts,
            scope=scope,
            focus_entities=focus_entities or [],
            original_query=query,
            confidence=confidence,
            query_patterns=query_patterns
        )
    
    def _extract_context_clues(self, query: str, concepts: List[str]) -> List[str]:
        """Extract context clues around identified concepts."""
        clues = []
        query_lower = query.lower()
        
        for concept in concepts:
            # Find words around the concept
            pattern = rf'\b\w+\s+{re.escape(concept)}\s+\w+\b|\b{re.escape(concept)}\s+\w+\b|\b\w+\s+{re.escape(concept)}\b'
            matches = re.findall(pattern, query_lower)
            clues.extend(matches)
        
        return list(set(clues))
    
    def _calculate_confidence(self, query: str, intent: QueryIntent, concepts: List[DomainConcept]) -> float:
        """Calculate confidence score for the analysis."""
        base_confidence = 0.5
        
        # Boost confidence based on clear intent patterns
        query_lower = query.lower()
        intent_patterns = self.intent_patterns.get(intent, [])
        pattern_matches = sum(1 for pattern in intent_patterns if re.search(pattern, query_lower))
        
        if pattern_matches > 0:
            base_confidence += 0.3
        
        # Boost confidence based on domain concept extraction
        if concepts:
            avg_concept_confidence = sum(c.confidence for c in concepts) / len(concepts)
            base_confidence += avg_concept_confidence * 0.2
        
        # Boost confidence for specific technical terms
        technical_terms = len([word for word in query_lower.split() if word in self.technical_indicators])
        if technical_terms > 0:
            base_confidence += min(technical_terms * 0.1, 0.2)
        
        return min(base_confidence, 1.0)
    
    def _extract_query_patterns(self, query: str) -> List[str]:
        """Extract patterns from the query for debugging purposes."""
        patterns = []
        query_lower = query.lower()
        
        # Question patterns
        if query_lower.startswith(('how', 'what', 'where', 'when', 'why', 'which')):
            patterns.append(f"question_word: {query_lower.split()[0]}")
        
        # Action patterns
        action_words = ['implement', 'create', 'add', 'fix', 'debug', 'analyze', 'review']
        for action in action_words:
            if action in query_lower:
                patterns.append(f"action: {action}")
        
        # Technical patterns
        if any(word in query_lower for word in self.technical_indicators):
            patterns.append("technical_focus")
        
        if any(word in query_lower for word in self.business_indicators):
            patterns.append("business_focus")
        
        return patterns


def test_query_intent_analyzer():
    """Test the QueryIntentAnalyzer with sample queries."""
    analyzer = QueryIntentAnalyzer()
    
    test_queries = [
        "How does the authentication system work?",
        "What handles user login validation?",
        "Why is the payment processing failing?",
        "How to add a new user registration feature?",
        "What's the overall architecture of the system?",
        "Which component is responsible for file parsing?",
        "Explain the data flow in the order processing pipeline",
        "How to optimize the database query performance?",
        "What security measures are implemented for API access?"
    ]
    
    print("🧪 Testing Query Intent Analyzer")
    print("=" * 60)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: {query}")
        context = analyzer.analyze_complete_query(query)
        
        print(f"   Intent: {context.intent.value}")
        print(f"   Scope: {context.scope.value}")
        print(f"   Confidence: {context.confidence:.2f}")
        print(f"   Domain Concepts: {len(context.domain_concepts)}")
        for concept in context.domain_concepts:
            print(f"     - {concept.category}: {concept.concept} (confidence: {concept.confidence:.2f})")
        print(f"   Query Patterns: {context.query_patterns}")


if __name__ == "__main__":
    test_query_intent_analyzer()
