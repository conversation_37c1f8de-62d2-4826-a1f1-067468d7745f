#!/usr/bin/env python3
"""
Real-World Aider System Testing

This script tests the Enhanced Metadata System with actual Aider-specific queries
and evaluates whether the generated packages provide appropriate context based on
deep understanding of the Aider codebase architecture.
"""

import time
from datetime import datetime
from enhanced_metadata_comprehensive_test import EnhancedMetadata<PERSON>estRunner
from test_results_analyzer import TestResultsAnalyzer


class AiderSystemEvaluator:
    """
    Evaluates Enhanced Metadata System packages against real Aider system knowledge.
    """
    
    def __init__(self):
        self.runner = EnhancedMetadataTestRunner(".")
        self.aider_knowledge = self._build_aider_knowledge_base()
    
    def _build_aider_knowledge_base(self):
        """Build knowledge base of Aider system architecture."""
        return {
            "core_components": {
                "coders": ["BaseCoder", "EditBlockCoder", "WholeFileCoder", "UnifiedDiffCoder"],
                "models": ["Model", "ModelSettings", "OpenAIModel", "AnthropicModel"],
                "io": ["InputOutput", "CaptureIO"],
                "repo": ["GitRepo", "RepoMap"],
                "context": ["ContextRequestHandler", "SurgicalExtractor"]
            },
            "key_workflows": {
                "chat_loop": ["run", "get_user_input", "send", "apply_updates"],
                "file_editing": ["apply_edits", "do_edit", "update_files"],
                "context_gathering": ["get_repo_map", "get_context", "extract_symbols"],
                "model_interaction": ["send_with_retries", "get_completion"]
            },
            "integration_points": {
                "git": ["GitRepo", "commit", "get_tracked_files"],
                "llm_apis": ["litellm", "openai", "anthropic"],
                "file_system": ["Path", "read_text", "write_text"]
            },
            "error_handling": {
                "exceptions": ["UnknownEditFormat", "MissingAPIKeyError", "FinishReasonLength"],
                "validation": ["validate_edit_format", "check_for_file_mentions"]
            }
        }
    
    def evaluate_package_relevance(self, query: str, package: str, expected_components: list) -> dict:
        """
        Evaluate if the generated package contains relevant Aider components.
        
        Args:
            query: The user query
            package: Generated LLM package
            expected_components: List of expected component names/types
            
        Returns:
            Evaluation results dictionary
        """
        evaluation = {
            "query": query,
            "expected_components": expected_components,
            "found_components": [],
            "missing_components": [],
            "unexpected_components": [],
            "relevance_score": 0.0,
            "context_quality": "unknown",
            "recommendations": []
        }
        
        # Extract component names from package
        package_lower = package.lower()
        found_any_expected = False
        
        for component in expected_components:
            if component.lower() in package_lower:
                evaluation["found_components"].append(component)
                found_any_expected = True
            else:
                evaluation["missing_components"].append(component)
        
        # Calculate relevance score
        if expected_components:
            evaluation["relevance_score"] = len(evaluation["found_components"]) / len(expected_components)
        
        # Determine context quality
        if evaluation["relevance_score"] >= 0.8:
            evaluation["context_quality"] = "excellent"
        elif evaluation["relevance_score"] >= 0.6:
            evaluation["context_quality"] = "good"
        elif evaluation["relevance_score"] >= 0.4:
            evaluation["context_quality"] = "fair"
        else:
            evaluation["context_quality"] = "poor"
        
        # Generate recommendations
        if evaluation["missing_components"]:
            evaluation["recommendations"].append(
                f"Missing key components: {', '.join(evaluation['missing_components'])}"
            )
        
        if evaluation["relevance_score"] < 0.6:
            evaluation["recommendations"].append(
                "Consider improving query-to-component mapping for better relevance"
            )
        
        return evaluation


def run_real_world_aider_test():
    """Run real-world Aider system tests."""
    print("🎯 Real-World Aider System Testing")
    print("=" * 60)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    evaluator = AiderSystemEvaluator()
    
    # Define real Aider queries with expected components
    aider_queries = [
        {
            "query": "How does Aider's chat loop work?",
            "category": "Core Workflow",
            "expected_components": ["BaseCoder", "run", "get_user_input", "send", "InputOutput"],
            "description": "Understanding the main chat interaction loop"
        },
        {
            "query": "How does Aider apply edits to files?",
            "category": "File Editing",
            "expected_components": ["apply_edits", "do_edit", "EditBlockCoder", "update_files"],
            "description": "File modification workflow"
        },
        {
            "query": "How does Aider gather context from the repository?",
            "category": "Context System",
            "expected_components": ["RepoMap", "get_repo_map", "ContextRequestHandler", "extract_symbols"],
            "description": "Repository context gathering system"
        },
        {
            "query": "How does Aider interact with LLM APIs?",
            "category": "LLM Integration",
            "expected_components": ["Model", "send_with_retries", "litellm", "get_completion"],
            "description": "Language model API integration"
        },
        {
            "query": "How does Aider handle Git operations?",
            "category": "Git Integration",
            "expected_components": ["GitRepo", "commit", "get_tracked_files", "git"],
            "description": "Git repository management"
        },
        {
            "query": "How does Aider handle different edit formats?",
            "category": "Edit Processing",
            "expected_components": ["EditBlockCoder", "WholeFileCoder", "UnifiedDiffCoder", "validate_edit_format"],
            "description": "Different code editing approaches"
        },
        {
            "query": "How does Aider handle errors and exceptions?",
            "category": "Error Handling",
            "expected_components": ["UnknownEditFormat", "MissingAPIKeyError", "FinishReasonLength"],
            "description": "Error handling and recovery"
        },
        {
            "query": "What is Aider's overall architecture?",
            "category": "System Architecture",
            "expected_components": ["BaseCoder", "Model", "GitRepo", "InputOutput", "RepoMap"],
            "description": "High-level system architecture"
        }
    ]
    
    print(f"📋 Testing {len(aider_queries)} real Aider queries:")
    for i, query_info in enumerate(aider_queries, 1):
        print(f"   {i}. {query_info['category']}: {query_info['query']}")
    print()
    
    # Execute queries and evaluate results
    print("🔍 Executing Real-World Aider Queries...")
    print("-" * 60)
    
    results = []
    evaluations = []
    
    for i, query_info in enumerate(aider_queries, 1):
        print(f"\n🧪 Test {i}: {query_info['category']}")
        print(f"   Query: {query_info['query']}")
        print(f"   Expected: {', '.join(query_info['expected_components'][:3])}...")
        
        # Execute the query
        result = evaluator.runner.execute_test_query(
            query_id=i,
            query=query_info['query'],
            expected_strategy="REAL_WORLD_TEST",
            category=query_info['category']
        )
        results.append(result)
        
        if result.success:
            # Evaluate the package relevance
            evaluation = evaluator.evaluate_package_relevance(
                query_info['query'],
                result.package,
                query_info['expected_components']
            )
            evaluations.append(evaluation)
            
            print(f"   ✅ Generated package: {len(result.package):,} chars")
            print(f"   📊 Relevance Score: {evaluation['relevance_score']:.1%}")
            print(f"   🎯 Context Quality: {evaluation['context_quality']}")
            print(f"   ✅ Found: {len(evaluation['found_components'])}/{len(evaluation['expected_components'])} expected components")
            
            if evaluation['missing_components']:
                print(f"   ⚠️  Missing: {', '.join(evaluation['missing_components'][:3])}...")
        else:
            print(f"   ❌ Failed: {result.error}")
            evaluations.append(None)
    
    # Generate comprehensive evaluation report
    print("\n" + "=" * 60)
    print("📊 REAL-WORLD AIDER SYSTEM EVALUATION")
    print("=" * 60)
    
    successful_evaluations = [e for e in evaluations if e is not None]
    
    if successful_evaluations:
        avg_relevance = sum(e['relevance_score'] for e in successful_evaluations) / len(successful_evaluations)
        quality_distribution = {}
        for e in successful_evaluations:
            quality = e['context_quality']
            quality_distribution[quality] = quality_distribution.get(quality, 0) + 1
        
        print(f"🎯 Average Relevance Score: {avg_relevance:.1%}")
        print(f"📈 Success Rate: {len(successful_evaluations)}/{len(aider_queries)} ({len(successful_evaluations)/len(aider_queries):.1%})")
        print()
        
        print("📊 Context Quality Distribution:")
        for quality, count in quality_distribution.items():
            print(f"   {quality.title()}: {count} queries ({count/len(successful_evaluations):.1%})")
        print()
        
        # Detailed results by category
        print("📋 DETAILED EVALUATION BY CATEGORY")
        print("-" * 60)
        
        for i, (query_info, evaluation) in enumerate(zip(aider_queries, evaluations), 1):
            if evaluation:
                status = "✅" if evaluation['relevance_score'] >= 0.6 else "⚠️" if evaluation['relevance_score'] >= 0.4 else "❌"
                print(f"{status} {query_info['category']}")
                print(f"   Relevance: {evaluation['relevance_score']:.1%} | Quality: {evaluation['context_quality']}")
                print(f"   Found: {', '.join(evaluation['found_components'][:3])}...")
                if evaluation['missing_components']:
                    print(f"   Missing: {', '.join(evaluation['missing_components'][:2])}...")
                print()
        
        # Overall assessment
        print("🏆 OVERALL ASSESSMENT")
        print("-" * 40)
        
        if avg_relevance >= 0.8:
            print("🌟 EXCELLENT: Enhanced Metadata System provides highly relevant Aider context")
        elif avg_relevance >= 0.6:
            print("✅ GOOD: System provides relevant context with room for improvement")
        elif avg_relevance >= 0.4:
            print("⚠️ FAIR: System provides some relevant context but needs enhancement")
        else:
            print("❌ POOR: System needs significant improvement for Aider-specific queries")
        
        print(f"\nThe Enhanced Metadata System achieved {avg_relevance:.1%} relevance")
        print("when tested against real-world Aider system queries.")
        
        return avg_relevance >= 0.6
    
    else:
        print("❌ No successful evaluations to analyze")
        return False


if __name__ == "__main__":
    try:
        success = run_real_world_aider_test()
        print(f"\n{'🎉 Real-world testing successful!' if success else '⚠️ Real-world testing needs improvement'}")
        exit_code = 0 if success else 1
    except Exception as e:
        print(f"\n❌ Real-world test failed: {e}")
        import traceback
        traceback.print_exc()
        exit_code = 1
    
    exit(exit_code)
