"""
Hierarchical Context Selector
Use system architecture to make intelligent context selection decisions.
"""

from typing import Dict, List, Any, Set, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json

from .system_architecture_generator import SystemArchitectureGenerator, SystemArchitecture, ModuleCluster
from .intelligent_semantic_selector import IntelligentSemanticSelector
from .intelligent_context_models import QueryContext


class ContextSelectionStrategy(Enum):
    """Strategies for context selection."""
    WORKFLOW_FOCUSED = "workflow_focused"      # Focus on specific workflow
    ARCHITECTURE_OVERVIEW = "architecture_overview"  # Broad architectural view
    CLUSTER_DEEP_DIVE = "cluster_deep_dive"   # Deep dive into specific cluster
    CROSS_CUTTING = "cross_cutting"           # Cross-cutting concerns


@dataclass
class ArchitecturalContext:
    """Context selection based on architectural understanding."""
    strategy: ContextSelectionStrategy
    primary_clusters: List[str]
    supporting_clusters: List[str]
    workflow_path: List[str]
    architectural_rationale: str
    selected_entities: List[Dict[str, Any]]


class HierarchicalContextSelector:
    """Select context using hierarchical architectural understanding."""
    
    def __init__(self):
        self.architecture_generator = SystemArchitectureGenerator()
        self.semantic_selector = IntelligentSemanticSelector()
        self.system_architecture = None
    
    def select_hierarchical_context(self, ir_data: Dict[str, Any], user_query: str,
                                   focus_entities: List[str] = None,
                                   max_entities: int = 8) -> Dict[str, Any]:
        """
        Select context using hierarchical architectural understanding.
        
        Args:
            ir_data: IR data containing system information
            user_query: User's query
            focus_entities: Entities to focus on
            max_entities: Maximum entities to select
            
        Returns:
            Enhanced context with architectural understanding
        """
        print("🏗️ Starting Hierarchical Context Selection...")

        # Store focus entities for use in scoring
        self._current_focus_entities = focus_entities or []

        # Step 1: Generate system architecture if not cached
        if not self.system_architecture:
            print("   📊 Generating system architecture...")
            self.system_architecture = self.architecture_generator.generate_architecture(ir_data)
            print(f"   ✅ Architecture generated with {len(self.system_architecture.clusters)} clusters")

        # Step 2: Analyze query semantically
        print("   🧠 Analyzing query semantically...")
        query_analysis = self.semantic_selector.analyze_query(user_query)
        # Store original query for enhanced scoring
        query_analysis.original_query = user_query
        print(f"   ✅ Query intent: {query_analysis.intent.value}")
        
        # Step 3: Determine context selection strategy
        strategy = self._determine_selection_strategy(query_analysis, focus_entities)
        print(f"   🎯 Selection strategy: {strategy.value}")
        
        # Step 4: Select relevant clusters based on strategy
        architectural_context = self._select_architectural_context(
            strategy, query_analysis, focus_entities, max_entities
        )
        
        # Step 5: Select specific entities within chosen clusters
        selected_entities = self._select_entities_from_clusters(
            architectural_context, ir_data, query_analysis, max_entities, strategy
        )
        
        # Step 6: Generate architectural explanation
        explanation = self._generate_architectural_explanation(
            architectural_context, selected_entities, query_analysis
        )
        
        print(f"   ✅ Selected {len(selected_entities)} entities from {len(architectural_context.primary_clusters)} primary clusters")
        
        return {
            'strategy': strategy.value,
            'architectural_context': architectural_context,
            'selected_entities': selected_entities,
            'architectural_explanation': explanation,
            'system_architecture': self.system_architecture,
            'query_analysis': query_analysis
        }
    
    def _determine_selection_strategy(self, query_analysis: QueryContext,
                                    focus_entities: List[str] = None) -> ContextSelectionStrategy:
        """Determine the best context selection strategy."""
        intent = query_analysis.intent
        scope = query_analysis.scope
        
        # Strategy decision logic
        if intent.value in ['workflow_analysis', 'debugging_assistance']:
            return ContextSelectionStrategy.WORKFLOW_FOCUSED
        elif intent.value in ['architecture_understanding', 'system_overview']:
            return ContextSelectionStrategy.ARCHITECTURE_OVERVIEW
        elif scope.value == 'single_component' or (focus_entities and len(focus_entities) <= 2):
            return ContextSelectionStrategy.CLUSTER_DEEP_DIVE
        else:
            return ContextSelectionStrategy.CROSS_CUTTING
    
    def _select_architectural_context(self, strategy: ContextSelectionStrategy,
                                    query_analysis: QueryContext,
                                    focus_entities: List[str],
                                    max_entities: int) -> ArchitecturalContext:
        """Select architectural context based on strategy."""
        
        if strategy == ContextSelectionStrategy.WORKFLOW_FOCUSED:
            return self._select_workflow_context(query_analysis, focus_entities)
        elif strategy == ContextSelectionStrategy.ARCHITECTURE_OVERVIEW:
            return self._select_overview_context(query_analysis)
        elif strategy == ContextSelectionStrategy.CLUSTER_DEEP_DIVE:
            return self._select_cluster_context(query_analysis, focus_entities)
        else:  # CROSS_CUTTING
            return self._select_cross_cutting_context(query_analysis, focus_entities)
    
    def _select_workflow_context(self, query_analysis: QueryContext,
                               focus_entities: List[str]) -> ArchitecturalContext:
        """Select context for workflow-focused analysis."""
        # For workflow analysis, focus on core business logic and context processing
        primary_clusters = []

        # Always include core business logic
        core_clusters = [c.name for c in self.system_architecture.clusters
                        if c.module_type.value == 'core_business']
        primary_clusters.extend(core_clusters[:1])  # Top 1 core cluster

        # Include context processing for workflow analysis
        context_clusters = [c.name for c in self.system_architecture.clusters
                           if 'context' in c.name.lower()]
        primary_clusters.extend(context_clusters[:1])  # Top 1 context cluster

        # Supporting clusters based on query intent
        supporting_clusters = []
        if query_analysis.intent.value == 'security_analysis':
            # Add models/external for security workflows
            model_clusters = [c.name for c in self.system_architecture.clusters
                             if c.module_type.value == 'external_integration']
            supporting_clusters.extend(model_clusters[:1])
        else:
            # Add infrastructure for general workflows
            infra_clusters = [c.name for c in self.system_architecture.clusters
                             if c.module_type.value == 'infrastructure']
            supporting_clusters.extend(infra_clusters[:1])

        workflow_path = primary_clusters + supporting_clusters

        rationale = f"Selected core business logic and context processing clusters for {query_analysis.intent.value} workflow"

        return ArchitecturalContext(
            strategy=ContextSelectionStrategy.WORKFLOW_FOCUSED,
            primary_clusters=primary_clusters,
            supporting_clusters=supporting_clusters,
            workflow_path=workflow_path,
            architectural_rationale=rationale,
            selected_entities=[]
        )
    
    def _select_overview_context(self, query_analysis: QueryContext) -> ArchitecturalContext:
        """Select context for architectural overview."""
        # Select one cluster from each layer
        primary_clusters = []
        for layer, clusters in self.system_architecture.layer_hierarchy.items():
            if clusters:
                # Pick the most critical cluster from each layer
                layer_clusters = [c for c in self.system_architecture.clusters if c.name in clusters]
                if layer_clusters:
                    most_critical = max(layer_clusters, key=lambda c: c.criticality)
                    primary_clusters.append(most_critical.name)
        
        supporting_clusters = []
        workflow_path = primary_clusters
        
        rationale = f"Selected representative clusters from each architectural layer for system overview"
        
        return ArchitecturalContext(
            strategy=ContextSelectionStrategy.ARCHITECTURE_OVERVIEW,
            primary_clusters=primary_clusters,
            supporting_clusters=supporting_clusters,
            workflow_path=workflow_path,
            architectural_rationale=rationale,
            selected_entities=[]
        )
    
    def _select_cluster_context(self, query_analysis: QueryContext,
                              focus_entities: List[str]) -> ArchitecturalContext:
        """Select context for cluster deep dive."""
        # Find cluster that contains focus entities
        target_cluster = None
        if focus_entities:
            for cluster in self.system_architecture.clusters:
                if any(entity.lower() in ' '.join(cluster.key_entities).lower() 
                      for entity in focus_entities):
                    target_cluster = cluster
                    break
        
        if not target_cluster:
            # Default to most critical cluster
            target_cluster = max(self.system_architecture.clusters, key=lambda c: c.criticality)
        
        primary_clusters = [target_cluster.name]
        supporting_clusters = target_cluster.dependencies[:2]  # Top 2 dependencies
        
        workflow_path = [target_cluster.name] + supporting_clusters
        
        rationale = f"Deep dive into '{target_cluster.name}' cluster based on focus entities and criticality"
        
        return ArchitecturalContext(
            strategy=ContextSelectionStrategy.CLUSTER_DEEP_DIVE,
            primary_clusters=primary_clusters,
            supporting_clusters=supporting_clusters,
            workflow_path=workflow_path,
            architectural_rationale=rationale,
            selected_entities=[]
        )
    
    def _select_cross_cutting_context(self, query_analysis: QueryContext,
                                    focus_entities: List[str]) -> ArchitecturalContext:
        """Select context for cross-cutting concerns."""
        # Select clusters that span multiple layers for cross-cutting analysis
        primary_clusters = []

        # For cross-cutting, focus on utility and infrastructure clusters
        util_clusters = [c.name for c in self.system_architecture.clusters
                        if c.module_type.value == 'utility']
        primary_clusters.extend(util_clusters[:1])

        # Include infrastructure clusters
        infra_clusters = [c.name for c in self.system_architecture.clusters
                         if c.module_type.value == 'infrastructure' and c.name not in primary_clusters]
        primary_clusters.extend(infra_clusters[:2])  # Top 2 infrastructure clusters

        # Supporting clusters - add core for context
        supporting_clusters = []
        core_clusters = [c.name for c in self.system_architecture.clusters
                        if c.module_type.value == 'core_business']
        supporting_clusters.extend(core_clusters[:1])

        workflow_path = primary_clusters + supporting_clusters

        rationale = f"Selected utility and infrastructure clusters for cross-cutting {query_analysis.intent.value} analysis"

        return ArchitecturalContext(
            strategy=ContextSelectionStrategy.CROSS_CUTTING,
            primary_clusters=primary_clusters,
            supporting_clusters=supporting_clusters,
            workflow_path=workflow_path,
            architectural_rationale=rationale,
            selected_entities=[]
        )
    
    def _trace_workflow_path(self, clusters: List[str]) -> List[str]:
        """Trace workflow path through clusters."""
        # Simple implementation - could be more sophisticated
        return clusters
    
    def _select_entities_from_clusters(self, architectural_context: ArchitecturalContext,
                                     ir_data: Dict[str, Any], query_analysis: QueryContext,
                                     max_entities: int, strategy: ContextSelectionStrategy) -> List[Dict[str, Any]]:
        """REFACTORED: Select entities using GLOBAL scoring instead of per-cluster limits."""
        print("🔧 REFACTORED: Using global entity selection algorithm")

        # Get all clusters to search
        all_clusters = architectural_context.primary_clusters + architectural_context.supporting_clusters
        print(f"   🎯 Searching clusters: {all_clusters}")

        # Find modules in these clusters
        cluster_modules = {}
        for cluster in self.system_architecture.clusters:
            if cluster.name in all_clusters:
                cluster_modules[cluster.name] = cluster.modules

        # CRITICAL FIX: Collect ALL entities from ALL clusters first
        all_candidate_entities = []

        for cluster_name in all_clusters:
            modules = cluster_modules.get(cluster_name, [])
            cluster_entity_count = 0

            # Get entities from modules in this cluster
            for module in ir_data.get('modules', []):
                if module.get('name', '') in modules:
                    for entity in module.get('entities', []):
                        entity_with_metadata = entity.copy()
                        entity_with_metadata['cluster'] = cluster_name
                        entity_with_metadata['module_name'] = module.get('name', '')
                        entity_with_metadata['file_path'] = module.get('file', '')
                        all_candidate_entities.append(entity_with_metadata)
                        cluster_entity_count += 1

            print(f"   📊 {cluster_name}: {cluster_entity_count} entities")

        print(f"   📊 Total candidate entities: {len(all_candidate_entities)}")

        # CRITICAL FIX: Score ALL entities globally, then select top ones
        print("   🧮 Scoring all entities globally...")
        all_scored_entities = self._score_entities_for_query(
            all_candidate_entities, query_analysis, strategy, "global"
        )

        # CRITICAL FIX: Select top entities globally (no per-cluster limits!)
        top_entities = all_scored_entities[:max_entities]

        print(f"   ✅ Selected {len(top_entities)} top entities globally")

        # Show what was selected
        for i, entity in enumerate(top_entities[:5], 1):
            name = entity.get('name', 'unknown')
            score = entity.get('relevance_score', 0)
            cluster = entity.get('cluster', 'unknown')
            print(f"      {i}. {name} (score: {score:.2f}, cluster: {cluster})")

        return top_entities

    def _ai_centric_entity_scoring(self, entity_name: str, entity_type: str,
                                 user_query: str, entity_file: str = "") -> float:
        """🧠 AI-CENTRIC SCORING: Think like an AI model - what context is most valuable?"""
        import re

        # 🎯 BUSINESS LOGIC ENTITIES (What the system actually DOES) - HIGHEST PRIORITY
        BUSINESS_LOGIC_PATTERNS = {
            # Position Management (Core Business Logic)
            r'Position(Opener|Closer|Manager)': 100.0,
            r'.*position.*(manager|handler|service)': 80.0,
            r'.*position.*(open|close|entry|exit)': 70.0,

            # Trading/Financial Logic
            r'.*Trade(Manager|Handler|Service)': 90.0,
            r'.*Order(Manager|Handler|Service)': 85.0,
            r'.*Portfolio(Manager|Handler)': 85.0,

            # Aider Core Workflow
            r'(apply_edits|prepare_to_edit|send_message)': 90.0,
            r'(run|run_one|get_files_content)': 80.0,

            # Model Management
            r'Model(?!.*Manager)': 85.0,  # Model class, not ModelManager
            r'.*model.*(register|config|info)': 75.0,
        }

        # 📊 DATA MODEL ENTITIES (Important for understanding structure)
        DATA_MODEL_PATTERNS = {
            r'^(Position|Trade|Order|Portfolio)$': 60.0,  # Core data models
            r'^(RepoMap|ContextRequest|QueryContext)$': 50.0,  # System data models
        }

        # 🔧 INFRASTRUCTURE ENTITIES (Supporting but not core) - LOWER PRIORITY
        INFRASTRUCTURE_PATTERNS = {
            r'.*Manager$': 20.0,  # Generic managers (DatabaseManager, etc.)
            r'.*Service$': 25.0,   # Generic services
            r'.*Handler$': 30.0,   # Generic handlers
            r'.*(Logger|Log).*': 10.0,  # Logging infrastructure
            r'.*(Config|Setting).*': 15.0,  # Configuration
            r'.*(Telegram|Notification).*': 5.0,  # Notification infrastructure
        }

        # 🚫 NOISE ENTITIES (Variables, utilities) - LOWEST PRIORITY
        NOISE_PATTERNS = {
            r'^[a-z_]+$': -10.0,  # Simple variable names (db_manager, event_system)
            r'.*_manager$': -5.0,  # Variable references to managers
            r'main': -20.0,        # Main functions
            r'.*test.*': -15.0,    # Test functions
        }

        score = 0.0

        # Apply pattern matching in priority order
        for pattern, boost in BUSINESS_LOGIC_PATTERNS.items():
            if re.search(pattern, entity_name, re.IGNORECASE):
                score += boost
                print(f"   🎯 BUSINESS LOGIC BOOST: {entity_name} +{boost}")
                break  # Only apply highest priority match

        if score == 0:  # No business logic match, try data models
            for pattern, boost in DATA_MODEL_PATTERNS.items():
                if re.search(pattern, entity_name, re.IGNORECASE):
                    score += boost
                    print(f"   📊 DATA MODEL BOOST: {entity_name} +{boost}")
                    break

        if score == 0:  # No data model match, try infrastructure
            for pattern, boost in INFRASTRUCTURE_PATTERNS.items():
                if re.search(pattern, entity_name, re.IGNORECASE):
                    score += boost
                    print(f"   🔧 INFRASTRUCTURE: {entity_name} +{boost}")
                    break

        # Always check for noise (can be negative)
        for pattern, penalty in NOISE_PATTERNS.items():
            if re.search(pattern, entity_name, re.IGNORECASE):
                score += penalty
                print(f"   🚫 NOISE PENALTY: {entity_name} {penalty}")
                break

        # 🎯 QUERY-SPECIFIC BOOSTS
        query_lower = user_query.lower()
        if 'position' in query_lower:
            if 'position' in entity_name.lower():
                score += 50.0  # MASSIVE boost for position-related queries
                print(f"   🚀 POSITION QUERY BOOST: {entity_name} +50.0")

        # Entity type bonuses (classes > functions > variables)
        if entity_type == 'class':
            score += 20.0
        elif entity_type == 'function':
            score += 10.0
        # Variables get no bonus (often just references)

        return max(score, 0.0)  # Never go below 0

    def _score_entities_for_query(self, entities: List[Dict[str, Any]],
                                query_analysis: QueryContext,
                                strategy: ContextSelectionStrategy = None,
                                cluster_name: str = None) -> List[Dict[str, Any]]:
        """ENHANCED Score entities based on query relevance and architectural strategy."""
        import re

        # Critical Aider methods that should be prioritized
        CRITICAL_AIDER_METHODS = {
            'apply_edits': 10.0, 'prepare_to_edit': 9.0, 'dirty_commit': 8.0,
            'send_message': 10.0, 'send': 9.0, 'run': 9.0, 'run_one': 8.0,
            'get_files_content': 8.0, 'apply_updates': 8.0, 'get_input': 7.0,
            'auto_commit': 7.0, 'format_messages': 7.0, 'show_send_output': 7.0,
            # Additional critical methods for comprehensive coverage
            'check_for_dirty_commit': 8.0, 'get_completion': 7.0, 'process_command': 7.0,
            'handle_command': 7.0, 'get_repo_map': 8.0, 'RepoMap': 8.0,
            'get_tracked_files': 7.0, 'register_models': 8.0, 'Model': 9.0,
            'get_model_info': 7.0, 'read_file': 7.0, 'write_file': 7.0,
            # POSITION MANAGEMENT CLASSES (HIGH PRIORITY)
            'PositionOpener': 10.0, 'PositionCloser': 10.0,
            'BacktestPositionManager': 8.0, 'position_exit_manager': 8.0,
            'position_entry_manager': 8.0, 'initialize_positions_if_needed': 7.0,
            'save_file': 7.0, 'backup_file': 6.0, 'get_file_content': 8.0,
            'UnknownEditFormat': 7.0, 'MissingAPIKeyError': 7.0, 'FinishReasonLength': 7.0,
            'handle_error': 6.0, 'show_error': 6.0, 'GitRepo': 8.0, 'build_context': 7.0,
            'get_context': 7.0, 'configure_model_settings': 6.0, 'sanity_check_model': 6.0
        }

        # Query-to-method mappings for explicit targeting
        QUERY_METHOD_MAPPINGS = {
            'file editing': ['apply_edits', 'prepare_to_edit', 'dirty_commit', 'apply_updates', 'check_for_dirty_commit'],
            'edit': ['apply_edits', 'prepare_to_edit', 'dirty_commit', 'check_for_dirty_commit'],
            'apply': ['apply_edits', 'apply_updates', 'prepare_to_edit'],
            'chat loop': ['run', 'run_one', 'send', 'get_input', 'process_command', 'handle_command'],
            'send message': ['send_message', 'send', 'format_messages', 'get_completion'],
            'llm': ['send_message', 'send', 'format_messages', 'show_send_output', 'get_completion'],
            'api': ['send_message', 'send', 'format_messages', 'show_send_output', 'get_completion'],
            'receive': ['send_message', 'send', 'format_messages', 'show_send_output', 'get_completion'],
            'responses': ['send_message', 'send', 'format_messages', 'show_send_output', 'get_completion'],
            'git': ['dirty_commit', 'auto_commit', 'repo', 'get_tracked_files', 'check_gitignore', 'GitRepo'],
            'context': ['get_files_content', 'get_repo_map', 'RepoMap', 'get_context', 'build_context'],
            'gather': ['get_files_content', 'get_repo_map', 'RepoMap', 'get_context', 'build_context'],
            'organize': ['get_files_content', 'get_repo_map', 'RepoMap', 'get_context', 'build_context'],
            'prompts': ['get_files_content', 'get_repo_map', 'RepoMap', 'get_context', 'build_context'],
            'architecture': ['Coder', 'Model', 'InputOutput', 'GitRepo'],
            'model': ['Model', 'register_models', 'get_model_info', 'configure_model_settings', 'sanity_check_model'],
            'manage': ['Model', 'register_models', 'get_model_info', 'configure_model_settings', 'sanity_check_model'],
            'configuration': ['Model', 'register_models', 'get_model_info', 'configure_model_settings', 'sanity_check_model'],
            'file': ['read_file', 'write_file', 'get_file_content', 'save_file', 'backup_file'],
            'read': ['read_file', 'get_file_content', 'get_files_content'],
            'write': ['write_file', 'save_file', 'apply_edits'],
            'repository': ['read_file', 'write_file', 'get_file_content', 'save_file', 'backup_file', 'GitRepo'],
            'error': ['UnknownEditFormat', 'MissingAPIKeyError', 'FinishReasonLength', 'handle_error', 'show_error'],
            'exception': ['UnknownEditFormat', 'MissingAPIKeyError', 'FinishReasonLength', 'handle_error', 'show_error'],
            'handle': ['UnknownEditFormat', 'MissingAPIKeyError', 'FinishReasonLength', 'handle_error', 'show_error', 'handle_command'],
            'interactive': ['run', 'run_one', 'get_input', 'process_command', 'handle_command'],
            'implement': ['run', 'run_one', 'get_input', 'process_command', 'handle_command'],
            'users': ['run', 'run_one', 'get_input', 'process_command', 'handle_command']
        }

        # Methods to PENALIZE (they're selected too often but are not core functionality)
        PENALIZED_METHODS = {
            'get_parser': -5.0, 'get_md_help': -5.0, '__init__': -3.0, 'main': -2.0
        }

        scored_entities = []
        user_query = getattr(query_analysis, 'original_query', '').lower()
        focus_entities = getattr(self, '_current_focus_entities', [])

        for entity in entities:
            entity_name = entity.get('name', '')
            entity_type = entity.get('type', '')
            entity_file = entity.get('file', '')

            # 🧠 PRIMARY: AI-CENTRIC SCORING (Think like an AI model!)
            print(f"   🧠 Scoring entity: {entity_name} ({entity_type})")
            score = self._ai_centric_entity_scoring(entity_name, entity_type, user_query, entity_file)

            # 1. CRITICAL: Focus entities get additional boost
            if focus_entities:
                for focus_entity in focus_entities:
                    if focus_entity.lower() in entity_name.lower():
                        score += 30.0  # Additional boost on top of AI scoring
                        print(f"   🎯 FOCUS BOOST: {entity_name} +30.0")

            # 4. PENALTY: Penalize over-selected irrelevant methods
            if entity_name in PENALIZED_METHODS:
                penalty = PENALIZED_METHODS[entity_name]
                score += penalty
                print(f"   ❌ PENALTY: {entity_name} {penalty}")

            # 5. Keyword matching boost
            if user_query:
                query_keywords = re.findall(r'\b\w+\b', user_query)
                for keyword in query_keywords:
                    if keyword in entity_name:
                        score += 5.0

            # 6. Type-based scoring
            if entity_type == 'class':
                score += 2.0  # Classes are generally important
            elif entity_type in ['method', 'function']:
                score += 1.0  # Methods are important

            # 7. Criticality from IR data
            criticality = entity.get('criticality_score', 0)
            score += criticality * 0.1  # Small boost from IR criticality

            # 8. Strategy-specific adjustments (reduced impact)
            if strategy == ContextSelectionStrategy.ARCHITECTURE_OVERVIEW and entity_type == 'class':
                score += 1.0
            elif strategy == ContextSelectionStrategy.WORKFLOW_FOCUSED and entity_type in ['function', 'method']:
                score += 1.0

            entity['relevance_score'] = score
            scored_entities.append(entity)

        # Sort by score
        scored_entities.sort(key=lambda e: e.get('relevance_score', 0.0), reverse=True)

        return scored_entities
    
    def _rank_final_selection(self, entities: List[Dict[str, Any]],
                            query_analysis: QueryContext, max_entities: int) -> List[Dict[str, Any]]:
        """DEPRECATED: Final ranking now done in global selection."""
        # This method is no longer needed since we do global selection
        # Just return the entities as-is (they're already sorted by score)
        return entities[:max_entities]
    
    def _generate_architectural_explanation(self, architectural_context: ArchitecturalContext,
                                          selected_entities: List[Dict[str, Any]],
                                          query_analysis: QueryContext) -> str:
        """Generate explanation of architectural context selection."""
        
        explanation = f"""🏗️ Architectural Context Selection

🎯 Strategy: {architectural_context.strategy.value.replace('_', ' ').title()}
📋 Rationale: {architectural_context.architectural_rationale}

🏛️ Primary Clusters: {', '.join(architectural_context.primary_clusters)}
🔗 Supporting Clusters: {', '.join(architectural_context.supporting_clusters)}
🔄 Workflow Path: {' → '.join(architectural_context.workflow_path)}

📊 Selected Components:
"""
        
        # Group entities by cluster
        entities_by_cluster = {}
        for entity in selected_entities:
            cluster = entity.get('cluster', 'unknown')
            if cluster not in entities_by_cluster:
                entities_by_cluster[cluster] = []
            entities_by_cluster[cluster].append(entity)
        
        for cluster, entities in entities_by_cluster.items():
            explanation += f"   • {cluster}: {len(entities)} components\n"
            for entity in entities[:3]:  # Show top 3
                name = entity.get('name', 'unknown')
                score = entity.get('relevance_score', 0.0)
                explanation += f"     - {name} (score: {score:.3f})\n"
        
        explanation += f"""
🧠 Query Analysis:
   • Intent: {query_analysis.intent.value}
   • Scope: {query_analysis.scope.value}
   • Confidence: {query_analysis.confidence:.2f}

This selection provides architectural context that spans the relevant system layers
and components needed to understand your query in the context of the overall system design."""
        
        return explanation
